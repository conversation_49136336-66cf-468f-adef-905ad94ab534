(function(){
  // Basic fade carousel
  const carousels = document.querySelectorAll('.carousel');
  carousels.forEach(function(c){
    const slides = c.querySelectorAll('.slide');
    let idx = 0;
    slides.forEach((s,i)=>{ s.style.opacity = (i===0?1:0); s.style.transition='opacity 900ms ease, transform 900ms ease'; });
    function tick(){
      const prev = idx;
      idx = (idx + 1) % slides.length;
      slides[prev].style.opacity = 0;
      slides[idx].style.opacity = 1;
      slides[idx].style.transform = 'scale(1.02)';
      setTimeout(()=>{ slides[idx].style.transform = 'scale(1)'; }, 900);
    }
    if (slides.length > 1) setInterval(tick, 4200);
  });

  // Lazy load images: add loading=lazy for non-first images
  document.querySelectorAll('img').forEach((img, i)=>{
    if (!img.getAttribute('loading')) {
      img.setAttribute('loading', i === 0 ? 'eager' : 'lazy');
    }
    img.setAttribute('decoding','async');
  });

  // Mobile menu functionality
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const mainNav = document.querySelector('.main-nav');
  const mobileNavOverlay = document.querySelector('.mobile-nav-overlay');
  const mobileNavClose = document.querySelector('.mobile-nav-close');
  const body = document.body;

  function toggleMobileMenu() {
    const isActive = mobileMenuToggle.classList.contains('active');

    if (isActive) {
      // Close menu
      mobileMenuToggle.classList.remove('active');
      mainNav.classList.remove('active');
      mobileNavOverlay.classList.remove('active');
      body.style.overflow = '';
    } else {
      // Open menu
      mobileMenuToggle.classList.add('active');
      mainNav.classList.add('active');
      mobileNavOverlay.classList.add('active');
      body.style.overflow = 'hidden';
    }
  }

  function closeMobileMenu() {
    mobileMenuToggle.classList.remove('active');
    mainNav.classList.remove('active');
    mobileNavOverlay.classList.remove('active');
    body.style.overflow = '';
  }

  if (mobileMenuToggle) {
    mobileMenuToggle.addEventListener('click', toggleMobileMenu);
  }

  if (mobileNavOverlay) {
    mobileNavOverlay.addEventListener('click', closeMobileMenu);
  }

  if (mobileNavClose) {
    mobileNavClose.addEventListener('click', closeMobileMenu);
  }

  // Close mobile menu when clicking on nav links
  if (mainNav) {
    const navLinks = mainNav.querySelectorAll('a');
    navLinks.forEach(link => {
      link.addEventListener('click', function(e) {
        // Allow the link to work normally, then close the menu
        setTimeout(closeMobileMenu, 100);
      });
    });
  }

  // Close mobile menu on window resize if screen becomes larger
  window.addEventListener('resize', function() {
    if (window.innerWidth > 768) {
      closeMobileMenu();
    }
  });

  // Handle escape key to close mobile menu
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeMobileMenu();
    }
  });
})();

