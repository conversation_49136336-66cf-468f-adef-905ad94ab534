#!/bin/bash

# Pecco项目Docker部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|logs|update]

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "系统要求检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    mkdir -p logs ssl
    chmod 755 logs ssl
}

# 启动服务
start_services() {
    log_info "启动Pecco服务..."
    check_requirements
    create_directories
    
    # 构建并启动服务
    docker-compose up -d --build
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_info "服务启动成功！"
        log_info "访问地址: http://localhost"
        log_info "管理后台: http://localhost/admin"
    else
        log_error "服务启动失败，请检查日志"
        docker-compose logs
        exit 1
    fi
}

# 停止服务
stop_services() {
    log_info "停止Pecco服务..."
    docker-compose down
    log_info "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启Pecco服务..."
    stop_services
    start_services
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose logs -f
}

# 更新服务
update_services() {
    log_info "更新Pecco服务..."
    
    # 拉取最新代码
    git pull origin main
    
    # 重新构建并启动
    docker-compose down
    docker-compose up -d --build
    
    log_info "服务更新完成"
}

# 创建超级用户
create_superuser() {
    log_info "创建Django超级用户..."
    docker-compose exec web python manage.py createsuperuser
}

# 数据库迁移
migrate_database() {
    log_info "执行数据库迁移..."
    docker-compose exec web python manage.py migrate
}

# 收集静态文件
collect_static() {
    log_info "收集静态文件..."
    docker-compose exec web python manage.py collectstatic --noinput
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    timestamp=$(date +%Y%m%d_%H%M%S)
    docker-compose exec mysql mysqldump -u pecco_user -ppecco_password_2024 pecco > "backup_${timestamp}.sql"
    log_info "数据库备份完成: backup_${timestamp}.sql"
}

# 显示帮助信息
show_help() {
    echo "Pecco项目Docker部署脚本"
    echo ""
    echo "使用方法:"
    echo "  ./deploy.sh [命令]"
    echo ""
    echo "可用命令:"
    echo "  start       启动所有服务"
    echo "  stop        停止所有服务"
    echo "  restart     重启所有服务"
    echo "  logs        查看服务日志"
    echo "  update      更新服务"
    echo "  superuser   创建超级用户"
    echo "  migrate     执行数据库迁移"
    echo "  static      收集静态文件"
    echo "  backup      备份数据库"
    echo "  help        显示此帮助信息"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs
            ;;
        update)
            update_services
            ;;
        superuser)
            create_superuser
            ;;
        migrate)
            migrate_database
            ;;
        static)
            collect_static
            ;;
        backup)
            backup_database
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
