version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: pecco_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: pecco
      MYSQL_USER: pecco_user
      MYSQL_PASSWORD: pecco_password_2024
      MYSQL_ROOT_PASSWORD: root_password_2024
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "3306:3306"
    networks:
      - pecco_network
    command: --default-authentication-plugin=mysql_native_password

  # Django应用
  web:
    build: ./pecco_backend
    container_name: pecco_web
    restart: unless-stopped
    environment:
      - DJANGO_SETTINGS_MODULE=pecco_backend.settings_production
      - MYSQL_HOST=mysql
      - MYSQL_DATABASE=pecco
      - MYSQL_USER=pecco_user
      - MYS<PERSON>_PASSWORD=pecco_password_2024
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - DEBUG=False
      - ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
    volumes:
      - static_volume:/app/static
      - media_volume:/app/media
    ports:
      - "8000:8000"
    depends_on:
      - mysql
    networks:
      - pecco_network
    command: >
      sh -c "
        echo 'Waiting for MySQL...' &&
        while ! nc -z mysql 3306; do sleep 1; done &&
        echo 'MySQL is ready!' &&
        python manage.py migrate &&
        python manage.py collectstatic --noinput &&
        gunicorn --bind 0.0.0.0:8000 --workers 3 pecco_backend.wsgi:application
      "

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: pecco_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - static_volume:/app/static:ro
      - media_volume:/app/media:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - web
    networks:
      - pecco_network

volumes:
  mysql_data:
  static_volume:
  media_volume:

networks:
  pecco_network:
    driver: bridge
