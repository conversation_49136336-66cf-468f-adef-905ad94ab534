# Pecco项目Docker部署指南

## 系统要求

- Ubuntu 22.04 LTS
- Docker 20.10+
- Docker Compose 2.0+
- 至少2GB内存，20GB磁盘空间

## 快速部署

### 1. 安装Docker和Docker Compose

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo apt install docker-compose -y

# 将当前用户添加到docker组
sudo usermod -aG docker $USER

# 重新登录或执行
newgrp docker
```

### 2. 克隆项目

```bash
git clone <your-repo-url>
cd pecco-pet-shop
```

### 3. 配置环境变量

编辑 `docker-compose.yml` 文件，修改以下配置：

```yaml
environment:
  - SECRET_KEY=your-super-secret-key-change-this-in-production
  - ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
```

### 4. 启动服务

```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 启动所有服务
./deploy.sh start
```

### 5. 创建超级用户

```bash
./deploy.sh superuser
```

## 详细配置

### 域名配置

1. 修改 `nginx/conf.d/pecco.conf` 中的 `server_name`
2. 修改 `docker-compose.yml` 中的 `ALLOWED_HOSTS`

### SSL证书配置

1. 将SSL证书文件放在 `ssl/` 目录下
2. 取消注释 `nginx/conf.d/pecco.conf` 中的HTTPS配置
3. 重启服务

### 邮件配置

在 `docker-compose.yml` 中添加邮件配置：

```yaml
environment:
  - USE_SMTP=true
  - EMAIL_HOST=smtp.qq.com
  - EMAIL_PORT=465
  - EMAIL_HOST_USER=<EMAIL>
  - EMAIL_HOST_PASSWORD=your-password
  - EMAIL_USE_SSL=true
```

## 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
./deploy.sh logs

# 重启服务
./deploy.sh restart

# 更新服务
./deploy.sh update

# 备份数据库
./deploy.sh backup

# 进入容器
docker-compose exec web bash
docker-compose exec mysql mysql -u pecco_user -p
```

## 监控和维护

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs web
docker-compose logs nginx
docker-compose logs mysql
```

### 数据备份

```bash
# 自动备份
./deploy.sh backup

# 手动备份
docker-compose exec mysql mysqldump -u pecco_user -ppecco_password_2024 pecco > backup.sql
```

### 性能优化

1. **调整worker数量**：修改Dockerfile中的gunicorn workers参数
2. **数据库优化**：调整MySQL配置参数
3. **静态文件CDN**：配置腾讯云CDN加速

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo netstat -tulpn | grep :80
   sudo kill -9 <PID>
   ```

2. **权限问题**
   ```bash
   sudo chown -R $USER:$USER .
   chmod +x deploy.sh
   ```

3. **数据库连接失败**
   ```bash
   docker-compose logs mysql
   docker-compose restart mysql
   ```

4. **静态文件404**
   ```bash
   ./deploy.sh static
   docker-compose restart nginx
   ```

### 重置环境

```bash
# 停止并删除所有容器
docker-compose down -v

# 删除镜像
docker rmi $(docker images -q)

# 重新部署
./deploy.sh start
```

## 安全建议

1. 修改默认密码
2. 配置防火墙
3. 定期更新系统和Docker
4. 启用SSL证书
5. 配置日志轮转

## 生产环境检查清单

- [ ] 修改SECRET_KEY
- [ ] 设置正确的ALLOWED_HOSTS
- [ ] 配置SSL证书
- [ ] 设置强密码
- [ ] 配置邮件服务
- [ ] 设置定期备份
- [ ] 配置监控告警
- [ ] 优化数据库配置
