#!/bin/bash

# Pecco项目一键自动部署脚本
# 适用于Ubuntu 22.04 + Docker部署

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
PROJECT_NAME="pecco-pet-shop"
DOMAIN="***************"  # 你的服务器IP
SECRET_KEY="pecco-super-secret-key-$(date +%s)"

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查系统
check_system() {
    log_step "检查系统环境..."
    
    if [[ $(lsb_release -rs) != "22.04" ]]; then
        log_warn "建议使用Ubuntu 22.04，当前版本: $(lsb_release -rs)"
    fi
    
    log_info "系统检查完成"
}

# 更新系统
update_system() {
    log_step "更新系统包..."
    sudo apt update && sudo apt upgrade -y
    sudo apt install -y curl wget git unzip
    log_info "系统更新完成"
}

# 安装Docker
install_docker() {
    log_step "安装Docker..."
    
    if command -v docker &> /dev/null; then
        log_info "Docker已安装，版本: $(docker --version)"
        return
    fi
    
    # 安装Docker
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    
    # 安装Docker Compose
    sudo apt install -y docker-compose
    
    # 添加用户到docker组
    sudo usermod -aG docker $USER
    
    # 启动Docker服务
    sudo systemctl enable docker
    sudo systemctl start docker
    
    log_info "Docker安装完成"
}

# 下载项目代码
download_project() {
    log_step "准备项目代码..."
    
    # 如果项目目录已存在，备份
    if [ -d "$PROJECT_NAME" ]; then
        log_warn "项目目录已存在，创建备份..."
        mv "$PROJECT_NAME" "${PROJECT_NAME}_backup_$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 这里你需要替换为你的实际git仓库地址
    # git clone https://github.com/your-username/pecco-pet-shop.git
    
    # 临时方案：创建项目结构
    mkdir -p $PROJECT_NAME
    cd $PROJECT_NAME
    
    log_info "项目代码准备完成"
}

# 创建配置文件
create_configs() {
    log_step "创建配置文件..."
    
    # 创建docker-compose.yml
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: pecco_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: pecco
      MYSQL_USER: pecco_user
      MYSQL_PASSWORD: pecco_password_2024
      MYSQL_ROOT_PASSWORD: root_password_2024
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "3306:3306"
    networks:
      - pecco_network
    command: --default-authentication-plugin=mysql_native_password

  web:
    build: ./pecco_backend
    container_name: pecco_web
    restart: unless-stopped
    environment:
      - DJANGO_SETTINGS_MODULE=pecco_backend.settings_production
      - MYSQL_HOST=mysql
      - MYSQL_DATABASE=pecco
      - MYSQL_USER=pecco_user
      - MYSQL_PASSWORD=pecco_password_2024
      - SECRET_KEY=REPLACE_SECRET_KEY
      - DEBUG=False
      - ALLOWED_HOSTS=REPLACE_DOMAIN
    volumes:
      - static_volume:/app/static
      - media_volume:/app/media
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      - mysql
    networks:
      - pecco_network
    command: >
      sh -c "
        echo 'Waiting for MySQL...' &&
        while ! nc -z mysql 3306; do sleep 1; done &&
        echo 'MySQL is ready!' &&
        python manage.py migrate &&
        python manage.py collectstatic --noinput &&
        gunicorn --bind 0.0.0.0:8000 --workers 3 pecco_backend.wsgi:application
      "

  nginx:
    image: nginx:alpine
    container_name: pecco_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - static_volume:/app/static:ro
      - media_volume:/app/media:ro
    depends_on:
      - web
    networks:
      - pecco_network

volumes:
  mysql_data:
  static_volume:
  media_volume:

networks:
  pecco_network:
    driver: bridge
EOF

    # 替换配置变量
    sed -i "s/REPLACE_SECRET_KEY/$SECRET_KEY/g" docker-compose.yml
    sed -i "s/REPLACE_DOMAIN/$DOMAIN/g" docker-compose.yml
    
    log_info "Docker Compose配置创建完成"
}

# 创建Nginx配置
create_nginx_config() {
    log_step "创建Nginx配置..."
    
    mkdir -p nginx/conf.d
    
    cat > nginx/nginx.conf << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    sendfile on;
    keepalive_timeout 65;
    
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    client_max_body_size 100M;
    
    include /etc/nginx/conf.d/*.conf;
}
EOF

    cat > nginx/conf.d/pecco.conf << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    location /static/ {
        alias /app/static/;
        expires 30d;
    }
    
    location /media/ {
        alias /app/media/;
        expires 7d;
    }
    
    location / {
        proxy_pass http://web:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

    log_info "Nginx配置创建完成"
}

# 创建MySQL初始化脚本
create_mysql_config() {
    log_step "创建MySQL配置..."
    
    mkdir -p mysql
    cat > mysql/init.sql << 'EOF'
CREATE DATABASE IF NOT EXISTS pecco DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'pecco_user'@'%' IDENTIFIED BY 'pecco_password_2024';
GRANT ALL PRIVILEGES ON pecco.* TO 'pecco_user'@'%';
FLUSH PRIVILEGES;
SET time_zone = '+08:00';
EOF

    log_info "MySQL配置创建完成"
}

# 创建Django项目结构（简化版）
create_django_structure() {
    log_step "创建Django项目结构..."
    
    mkdir -p pecco_backend/pecco_backend
    mkdir -p pecco_backend/templates
    mkdir -p pecco_backend/static
    mkdir -p pecco_backend/assets
    mkdir -p logs
    
    # 创建简化的Dockerfile
    cat > pecco_backend/Dockerfile << 'EOF'
FROM python:3.11-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

WORKDIR /app

RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    netcat-traditional \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

COPY . /app/

RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

EXPOSE 8000
EOF

    # 创建requirements.txt
    cat > pecco_backend/requirements.txt << 'EOF'
Django==4.2.13
PyMySQL==1.1.0
Pillow==10.3.0
python-dotenv==1.0.1
djangorestframework==3.15.2
gunicorn==21.2.0
mysqlclient==2.2.4
EOF

    log_info "Django项目结构创建完成"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    sudo ufw allow 22/tcp
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    sudo ufw --force enable
    
    log_info "防火墙配置完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    # 构建并启动服务
    docker-compose up -d --build
    
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_info "✅ 服务启动成功！"
        echo ""
        echo "🌐 访问地址:"
        echo "   网站首页: http://$DOMAIN"
        echo "   管理后台: http://$DOMAIN/admin"
        echo ""
        echo "📝 下一步:"
        echo "   1. 创建管理员账户: docker-compose exec web python manage.py createsuperuser"
        echo "   2. 上传你的实际项目代码"
        echo "   3. 配置域名和SSL证书"
    else
        log_error "❌ 服务启动失败"
        docker-compose logs
    fi
}

# 主函数
main() {
    echo "🚀 Pecco项目自动部署开始..."
    echo "📍 目标服务器: $DOMAIN"
    echo ""
    
    check_system
    update_system
    install_docker
    download_project
    create_configs
    create_nginx_config
    create_mysql_config
    create_django_structure
    configure_firewall
    start_services
    
    echo ""
    echo "🎉 部署完成！"
}

# 执行主函数
main "$@"
